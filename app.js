'use strict'

// CONFIG moved to the top, to be absolutely sure it's globally accessible
const CONFIG = {
  MAX_MESSAGE_SIZE: 4096, // Telegram's message size limit
  STREAM_TIMEOUT: 300000, // 5 minutes in milliseconds
  MAX_RESPONSE_SIZE: 1024 * 1024 // 1MB
};

import TelegramBot from 'node-telegram-bot-api';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import cron from 'node-cron';
import fs from 'fs/promises';
import path from 'path';

// Set timezone for consistent timestamps
process.env.TZ = process.env.TZ || 'America/New_York';
console.log('Server timezone:', process.env.TZ);
console.log('Current server time:', new Date().toLocaleString('en-US', { timeZone: process.env.TZ }));

// Get directory name for ES module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path for storing scheduled tasks
const TASKS_FILE_PATH = path.join(__dirname, 'scheduled_tasks.json');

// Load environment variables with specific path
dotenv.config({ path: `${__dirname}/.env` });

// --- HELPER FUNCTIONS & CONFIGURATION ORDERING ---

// Basic environment variable sanitization
function sanitizeEnvVar(value, name) {
  if (!value) return null;
  
  // Remove whitespace
  const cleaned = value.trim();
  
  // Basic validation
  if (cleaned.length < 1) {
    console.warn(`⚠️ Warning: ${name} is empty after sanitization`);
    return null;
  }
  
  return cleaned;
}

// Secure logging helper
function secureLog(type, message, data = null) {
  const timestamp = new Date().toLocaleString('en-US', { timeZone: process.env.TZ });
  switch(type) {
    case 'command':
      console.log(`[${timestamp}] 📥 Command: ${data.command} in chat ${data.chatId}`);
      break;
    case 'error':
      console.error(`[${timestamp}] ❌ Error: ${message}`);
      break;
    case 'info':
      console.log(`[${timestamp}] ℹ️ ${message}`);
      break;
    case 'success':
      console.log(`[${timestamp}] ✅ ${message}`);
      break;
  }
}

// Helper to truncate messages if needed
function safeTruncateMessage(message, maxLength = CONFIG.MAX_MESSAGE_SIZE) {
  if (typeof message !== 'string') return 'Invalid message format';
  if (message.length <= maxLength) return message;

  return message.slice(0, maxLength - 3) + '...';
}

// Helper to format responses as plain text
function formatAsPlainText(text) {
  if (typeof text !== 'string') return 'Invalid response format';

  let plainText = text;

  // Remove HTML tags
  plainText = plainText.replace(/<[^>]*>/g, '');

  // Convert markdown formatting to plain text
  // Remove bold/italic markers but keep the text
  plainText = plainText.replace(/\*\*([^*]+)\*\*/g, '$1'); // **bold**
  plainText = plainText.replace(/\*([^*]+)\*/g, '$1'); // *italic*
  plainText = plainText.replace(/__([^_]+)__/g, '$1'); // __bold__
  plainText = plainText.replace(/_([^_]+)_/g, '$1'); // _italic_

  // Remove code formatting but keep content
  plainText = plainText.replace(/`([^`]+)`/g, '$1'); // `code`
  plainText = plainText.replace(/```[\s\S]*?```/g, (match) => {
    // For code blocks, extract just the content
    return match.replace(/```[^\n]*\n?/g, '').replace(/```/g, '');
  });

  // Remove links but keep text
  plainText = plainText.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1'); // [text](url)
  plainText = plainText.replace(/https?:\/\/[^\s]+/g, ''); // standalone URLs

  // Remove other markdown elements
  plainText = plainText.replace(/^#+\s*/gm, ''); // headers
  plainText = plainText.replace(/^>\s*/gm, ''); // blockquotes
  plainText = plainText.replace(/^[-*+]\s*/gm, '• '); // list items
  plainText = plainText.replace(/^\d+\.\s*/gm, ''); // numbered lists

  // Clean up extra whitespace
  plainText = plainText.replace(/\n{3,}/g, '\n\n'); // max 2 consecutive newlines
  plainText = plainText.replace(/[ \t]+/g, ' '); // multiple spaces to single space
  plainText = plainText.trim();

  return plainText;
}

// Helper function for real-time message editing with rate limiting
async function updateMessageSafely(bot, chatId, messageId, text, lastUpdateTime = 0) {
  const now = Date.now();
  const timeSinceLastUpdate = now - lastUpdateTime;
  const minUpdateInterval = 1000; // 1 second minimum between updates

  // Rate limiting: don't update too frequently
  if (timeSinceLastUpdate < minUpdateInterval) {
    return lastUpdateTime;
  }

  try {
    await bot.editMessageText(formatAsPlainText(text), {
      chat_id: chatId,
      message_id: messageId
    });
    return now;
  } catch (error) {
    // Handle common Telegram API errors gracefully
    if (error.message.includes('message is not modified')) {
      // Message content is the same, no need to update
      return lastUpdateTime;
    } else if (error.message.includes('too many requests')) {
      // Rate limited, wait and try again later
      secureLog('warning', 'Rate limited on message editing');
      return lastUpdateTime;
    } else {
      secureLog('error', `Error updating message: ${error.message}`);
      return lastUpdateTime;
    }
  }
}

// Streaming state management
function createStreamingState() {
  return {
    fullResponse: '',
    lastUpdateTime: 0,
    pendingText: '',
    activeTools: new Set(),
    isComplete: false
  };
}

// Process different types of streaming chunks
async function processStreamChunk(chunk, streamState, bot, chatId, messageId) {
  switch (chunk.type) {
    case 'text':
      await processTextChunk(chunk, streamState, bot, chatId, messageId);
      break;
    case 'tool_call':
      await processToolCall(chunk, streamState, bot, chatId, messageId);
      break;
    case 'tool_result':
      await processToolResult(chunk, streamState, bot, chatId, messageId);
      break;
    case 'finish':
      await processFinish(chunk, streamState, bot, chatId, messageId);
      break;
    default:
      // Handle unknown chunk types gracefully
      secureLog('info', `Unknown chunk type: ${chunk.type}`);
  }
}

// Process text chunks with batching
async function processTextChunk(chunk, streamState, bot, chatId, messageId) {
  streamState.pendingText += chunk.value || '';
  streamState.fullResponse += chunk.value || '';

  // Update message with accumulated text
  streamState.lastUpdateTime = await updateMessageSafely(
    bot, chatId, messageId, streamState.fullResponse, streamState.lastUpdateTime
  );
}

// Process tool call chunks
async function processToolCall(chunk, streamState, bot, chatId, messageId) {
  const toolName = chunk.value?.name || 'unknown tool';
  streamState.activeTools.add(toolName);

  secureLog('info', `Streaming: Tool called: ${toolName}`);

  // Add tool indicator to response
  const toolIndicator = `\n\n🔧 Using ${toolName} tool...`;
  streamState.fullResponse += toolIndicator;

  // Immediate update for tool calls
  streamState.lastUpdateTime = await updateMessageSafely(
    bot, chatId, messageId, streamState.fullResponse, 0 // Force immediate update
  );
}

// Process tool result chunks
async function processToolResult(chunk, streamState, bot, chatId, messageId) {
  const toolCallId = chunk.value?.tool_call_id;

  secureLog('info', `Streaming: Tool result received for: ${toolCallId}`);

  // Add completion indicator
  const completionIndicator = `\n📊 Tool completed`;
  streamState.fullResponse += completionIndicator;

  // Immediate update for tool results
  streamState.lastUpdateTime = await updateMessageSafely(
    bot, chatId, messageId, streamState.fullResponse, 0 // Force immediate update
  );
}

// Process finish chunks
async function processFinish(chunk, streamState, bot, chatId, messageId) {
  streamState.isComplete = true;

  secureLog('info', 'Streaming: Stream finished');

  // Final update with completion indicator
  const finalResponse = streamState.fullResponse + '\n\n✅ Complete!';
  await updateMessageSafely(bot, chatId, messageId, finalResponse, 0);
}

// Helper function to escape Markdown characters for Telegram
function escapeMarkdown(text) {
  if (typeof text !== 'string') return '';
  // For parse_mode: 'Markdown' (not MarkdownV2), we mainly need to escape: _, *, `, [
  // Other characters like ., !, -, etc., are generally fine unless part of a more complex structure
  // not typically generated by simple task names/prompts.
  const escapeChars = /[_*`[\]]/g; // More targeted for parse_mode: Markdown
  return text.replace(escapeChars, '\\$&'); // Use $& to refer to the matched string directly
}

// Validate environment variables
const TELEGRAM_BOT_TOKEN = sanitizeEnvVar(process.env.TELEGRAM_BOT_TOKEN, 'TELEGRAM_BOT_TOKEN');
const HUSTLE_API_KEY = sanitizeEnvVar(process.env.HUSTLE_API_KEY, 'HUSTLE_API_KEY');
const ADMIN_USER_IDS_STRING = sanitizeEnvVar(process.env.ADMIN_USER_IDS, 'ADMIN_USER_IDS');

const adminUserIds = ADMIN_USER_IDS_STRING ? ADMIN_USER_IDS_STRING.split(',').map(id => parseInt(id.trim(), 10)) : [];

// More secure environment check
console.log('\nEnvironment check:');
console.log('Required environment variables:', 
  Object.entries({
    TELEGRAM_BOT_TOKEN: !!TELEGRAM_BOT_TOKEN,
    HUSTLE_API_KEY: !!HUSTLE_API_KEY
  }).map(([key, present]) => 
    `\n- ${key}: ${present ? '✅' : '❌'}`
  ).join('')
);

if (!TELEGRAM_BOT_TOKEN || !HUSTLE_API_KEY) {
  console.error('\n❌ Error: Missing required environment variables');
  console.error('Please check your .env file and ensure both TELEGRAM_BOT_TOKEN and HUSTLE_API_KEY are set');
  if (adminUserIds.length === 0) {
    console.warn("⚠️ Warning: ADMIN_USER_IDS is not set or empty in .env. Admin commands for automated tasks will not be available to any user.");
  }
  process.exit(1);
}

// Create a bot instance with updated polling configuration
const bot = new TelegramBot(TELEGRAM_BOT_TOKEN, {
  polling: true // Simplified polling configuration
});

// Message deduplication cache
const processedMessages = new Set();
const MESSAGE_CACHE_TIMEOUT = 5000; // 5 seconds

// In-memory store for scheduled tasks
let scheduledTasks = {}; // Format: { taskName: { cronJob, schedule, targetGroupId, prompts, running, createdBy } }

// Store bot info globally
let botInfo = null;

// Helper function to clean command text
function cleanCommand(command) {
  // Remove bot username from command if present
  if (botInfo && command.includes('@')) {
    const [baseCommand, botUsername] = command.split('@');
    if (botUsername === botInfo.username) {
      return baseCommand;
    }
    // If command is for a different bot, return null
    return null;
  }
  return command;
}

// Helper function to check if a message has been processed
function isMessageProcessed(msg) {
  const messageId = `${msg.chat.id}:${msg.message_id}`;
  if (processedMessages.has(messageId)) {
    return true;
  }
  processedMessages.add(messageId);
  setTimeout(() => processedMessages.delete(messageId), MESSAGE_CACHE_TIMEOUT);
  return false;
}

// Basic input validation
function validateUserInput(input, maxLength = 4096) {
  if (!input || typeof input !== 'string') return '';
  
  // Trim whitespace
  const cleaned = input.trim();
  
  // Check length (Telegram message limit is 4096 characters)
  if (cleaned.length > maxLength) {
    return cleaned.slice(0, maxLength);
  }
  
  return cleaned;
}

console.log('\nInitializing bot...');

// Direct API configuration for Agent Hustle
// Try different possible endpoints
const POSSIBLE_ENDPOINTS = [
  'https://agenthustle.ai/api/chat',
  'https://api.agenthustle.ai/chat',
  'https://agenthustle.ai/chat',
  'https://api.agenthustle.ai/v1/chat'
];

const HUSTLE_API_ENDPOINT = POSSIBLE_ENDPOINTS[0]; // Start with the first one

// Utility function to try different API configurations
async function tryHustleApiCall(messages, vaultId = 'default') {
  const authMethods = [
    { headers: { 'Content-Type': 'application/json', 'X-API-Key': HUSTLE_API_KEY } },
    { headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${HUSTLE_API_KEY}` } },
    { headers: { 'Content-Type': 'application/json', 'Authorization': `API-Key ${HUSTLE_API_KEY}` } },
    { headers: { 'Content-Type': 'application/json', 'api-key': HUSTLE_API_KEY } }
  ];

  const payloadFormats = [
    { messages, vaultId },
    { messages, vault_id: vaultId },
    { prompt: messages[0]?.content, vaultId },
    { input: messages[0]?.content, vaultId }
  ];

  for (const endpoint of POSSIBLE_ENDPOINTS) {
    for (const authMethod of authMethods) {
      for (const payload of payloadFormats) {
        try {
          secureLog('info', `Trying endpoint: ${endpoint}`);
          secureLog('info', `Auth headers: ${JSON.stringify(authMethod.headers)}`);
          secureLog('info', `Payload: ${JSON.stringify(payload)}`);

          const response = await fetch(endpoint, {
            method: 'POST',
            headers: authMethod.headers,
            body: JSON.stringify(payload)
          });

          secureLog('info', `Response status: ${response.status} ${response.statusText}`);

          if (response.ok) {
            const responseText = await response.text();
            secureLog('success', `✅ Found working configuration!`);
            secureLog('info', `Working endpoint: ${endpoint}`);
            secureLog('info', `Working auth: ${JSON.stringify(authMethod.headers)}`);
            secureLog('info', `Working payload: ${JSON.stringify(payload)}`);
            return responseText;
          } else {
            const errorText = await response.text();
            secureLog('warning', `❌ ${endpoint} returned ${response.status}: ${errorText.substring(0, 100)}`);
          }
        } catch (error) {
          secureLog('warning', `❌ ${endpoint} failed: ${error.message}`);
        }
      }
    }
  }

  throw new Error('All API endpoint configurations failed');
}

// Utility function for direct API calls to Agent Hustle
async function createHustleApiCall(messages, vaultId = 'default') {
  try {
    return await tryHustleApiCall(messages, vaultId);
  } catch (error) {
    secureLog('error', `Hustle API call failed: ${error.message}`);
    throw error;
  }
}

// Utility function for streaming API calls to Agent Hustle
async function* createHustleStreamCall(messages, vaultId = 'default') {
  try {
    secureLog('info', `Making streaming API request to: ${HUSTLE_API_ENDPOINT}`);

    // For now, fall back to regular API call and simulate streaming
    // This ensures compatibility while we test the basic functionality
    const response = await fetch(HUSTLE_API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': HUSTLE_API_KEY
      },
      body: JSON.stringify({
        messages: messages,
        vaultId: vaultId
      })
    });

    secureLog('info', `Streaming response status: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      secureLog('error', `Streaming API error response: ${errorText}`);
      throw new Error(`Streaming API request failed: ${response.status} ${response.statusText}`);
    }

    const responseText = await response.text();
    secureLog('info', `Streaming response received, length: ${responseText.length}`);

    // Simulate streaming by yielding text in chunks
    const words = responseText.split(' ');
    const chunkSize = 5; // 5 words per chunk

    for (let i = 0; i < words.length; i += chunkSize) {
      const chunk = words.slice(i, i + chunkSize).join(' ');
      yield { type: 'text', value: chunk + ' ' };

      // Small delay to simulate real streaming
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Signal completion
    yield { type: 'finish', value: 'complete' };

  } catch (error) {
    secureLog('error', `Hustle streaming API call failed: ${error.message}`);
    throw error;
  }
}

// Load tasks from file before starting bot logic that might use them
loadTasksFromFile().then(() => {
    // Verify bot connection and store bot info
    bot.getMe().then((info) => {
      botInfo = info;
      console.log('\n✅ Connected to Telegram as:');
      console.log(`Bot Username: @${info.username}`);
      console.log(`Bot Name: ${info.first_name}`);
    }).catch((error) => {
      console.error('\n❌ Failed to connect to Telegram:', error.message);
      process.exit(1);
    });

    // Helper function to check if user is an admin
    function isUserAdmin(userId) {
      return adminUserIds.includes(userId);
    }









    // Central command handler
    async function handleCommand(msg, command, args) {
      if (isMessageProcessed(msg)) {
        secureLog('info', 'Skipping already processed message');
        return;
      }

      const chatId = msg.chat.id;
      secureLog('command', '', { command, chatId });

      // Clean the command
      const cleanedCommand = cleanCommand(command);
      if (!cleanedCommand) {
        secureLog('info', 'Command for different bot, ignoring');
        return;
      }

      switch (cleanedCommand) {
        case '/start':
          const startMessage = `👋 Hello! I'm your Agent Hustle bot.

Available commands:
/start - Show this welcome message
/help - Show available commands
/hustle [message] - Chat with Agent Hustle
/hustlestream [message] - Chat with streaming responses

Try sending: /hustle Tell me about yourself`;

          try {
            await bot.sendMessage(chatId, startMessage);
            secureLog('success', 'Start message sent to chat');
          } catch (error) {
            secureLog('error', `Error sending start message: ${error.message}`);
          }
          break;

        case '/help':
          const helpMessage = `🤖 Available Commands:

/start - Start the bot
/help - Show this help message
/hustle [message] - Chat with Agent Hustle
/hustlestream [message] - Chat with streaming responses

Admin Commands (requires ADMIN_USER_IDS configuration):
/addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...] - Add a new automated task.
  Example: /addautotask daily_report "0 9 * * *" -100123456789 "Summarize crypto news" "Give me TNSR report"
/listautotasks - List all configured automated tasks.
/toggleautotask <name> - Start or stop an existing automated task.
/deleteautotask <name> - Delete an automated task.
/editautotask <name> [--schedule "new_schedule"] [--groupid <new_group_id>] [--prompts "prompt1" ...] - Edit an existing task.

Examples:
/hustle What can you do?
/hustlestream Tell me a story`;

          try {
            await bot.sendMessage(chatId, helpMessage);
            secureLog('success', 'Help message sent to chat');
          } catch (error) {
            secureLog('error', `Error sending help message: ${error.message}`);
          }
          break;

        case '/hustle':
          const userMessage = validateUserInput(args);
          if (!userMessage) {
            try {
              await bot.sendMessage(chatId, 'Please provide a message after /hustle\nExample: /hustle Tell me about yourself');
              secureLog('success', 'Empty message response sent');
              return;
            } catch (error) {
              secureLog('error', 'Error sending empty message response: ' + error.message);
              return;
            }
          }

          try {
            secureLog('info', 'Processing message with Agent Hustle...');
            await bot.sendChatAction(chatId, 'typing');

            const response = await createHustleApiCall([{
              role: 'user',
              content: userMessage
            }], `telegram-${chatId}`);

            secureLog('success', 'Response received');
            let messageToSend = typeof response === 'string' ? response : 'No response content';
            messageToSend = formatAsPlainText(messageToSend);
            messageToSend = safeTruncateMessage(messageToSend);

            await bot.sendMessage(chatId, messageToSend);
            secureLog('success', `Response sent to chat ${chatId}`);

          } catch (error) {
            secureLog('error', `Error processing hustle command: ${error.message}`);
            let errorMessage = 'Sorry, I encountered an error processing your request.';
            if (error.message.includes('500')) {
              errorMessage = 'Server error. Please try again in a few moments.';
            } else if (error.message.includes('API key')) {
              errorMessage = 'Authentication error. Please contact the bot administrator.';
            }
            try {
              await bot.sendMessage(chatId, errorMessage);
              secureLog('success', 'Error message sent to user');
            } catch (sendError) {
              secureLog('error', 'Failed to send error message: ' + sendError.message);
            }
          }
          break;

        case '/hustlestream':
          const streamMessage = args.trim();
          if (!streamMessage) {
            try {
              await bot.sendMessage(chatId, 'Please provide a message after /hustlestream\nExample: /hustlestream Tell me a story');
              secureLog('success', 'Empty message response sent');
              return;
            } catch (error) {
              secureLog('error', 'Error sending empty message response: ' + error.message);
              return;
            }
          }

          try {
            // Send initial message that will be updated in real-time
            const initialMessage = await bot.sendMessage(chatId, '🤖 Hustle is thinking...');
            const messageId = initialMessage.message_id;
            secureLog('info', 'Streaming: Processing message with Agent Hustle...');

            // Initialize streaming state
            const streamState = createStreamingState();

            // Start streaming request using direct API
            const stream = createHustleStreamCall([{
              role: 'user',
              content: streamMessage
            }], `telegram-${chatId}`);

            // Process stream chunks in real-time
            for await (const chunk of stream) {
              await processStreamChunk(chunk, streamState, bot, chatId, messageId);

              // Check for size limits
              if (streamState.fullResponse.length > CONFIG.MAX_RESPONSE_SIZE) {
                secureLog('warning', 'Streaming: Response size limit reached');
                break;
              }
            }

            // Ensure final message is properly formatted
            if (!streamState.isComplete) {
              const finalResponse = formatAsPlainText(streamState.fullResponse);
              await updateMessageSafely(bot, chatId, messageId, finalResponse, 0);
            }

            secureLog('success', 'Streaming: Response sent successfully');

          } catch (error) {
            secureLog('error', 'Streaming: Error processing command: ' + error.message);
            try {
              await bot.sendMessage(chatId, 'Sorry, I encountered an error with streaming. Please try the regular /hustle command instead.');
            } catch (sendError) {
              secureLog('error', 'Streaming: Failed to send error message: ' + sendError.message);
            }
          }
          break;

        // Admin commands for automated tasks
        case '/addautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /addautotask attempt by user ${msg.from.id}`);
            return;
          }
          // const taskArgs = args.split(' '); // Old problematic line

          // New argument parsing logic
          const matchedArgs = args.match(/"[^"]+"|\S+/g) || [];
          const parsedArgs = matchedArgs.map(arg => {
            if (arg.length >= 2 && arg.startsWith('"') && arg.endsWith('"')) {
              return arg.slice(1, -1); // Remove surrounding quotes
            }
            return arg;
          });

          if (parsedArgs.length < 4) {
            bot.sendMessage(chatId, `Usage: /addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...]\nParsed args count: ${parsedArgs.length}`);
            return;
          }
          const [taskName, cronSchedule, targetGroupIdStr, ...prompts] = parsedArgs;
          const targetGroupId = parseInt(targetGroupIdStr, 10);

          if (scheduledTasks[taskName]) {
            bot.sendMessage(chatId, `⚠️ Task "${taskName}" already exists. Use /deleteautotask first if you want to redefine it.`);
            return;
          }

          if (!cron.validate(cronSchedule)) {
            bot.sendMessage(chatId, `❌ Invalid cron schedule: "${cronSchedule}". Please use a valid cron format (e.g., "0 9 * * *").`);
            return;
          }
          
          if (isNaN(targetGroupId)) {
            bot.sendMessage(chatId, `❌ Invalid Target Group ID: "${targetGroupIdStr}". Must be a number.`);
            return;
          }

          if (prompts.length === 0) {
            bot.sendMessage(chatId, `❌ You must provide at least one prompt for the task.`);
            return;
          }

          try {
            const job = cron.schedule(cronSchedule, async () => {
              secureLog('info', `Running automated task: ${taskName}`);
              const randomPrompt = prompts[Math.floor(Math.random() * prompts.length)];
              try {
                await bot.sendChatAction(targetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${targetGroupId} for ${taskName}: ${e.message}`));
                
                const hustleResponse = await createHustleApiCall([{ role: 'user', content: randomPrompt }], `autotask-${taskName}`);
                let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : 'No content from Hustle.';
                messageToSend = formatAsPlainText(messageToSend);
                messageToSend = safeTruncateMessage(messageToSend);

                await bot.sendMessage(targetGroupId, messageToSend);
                secureLog('success', `Automated task "${taskName}" executed. Prompt: "${randomPrompt}". Response sent to group ${targetGroupId}.`);
              } catch (error) {
                secureLog('error', `Error executing automated task "${taskName}": ${error.message}`);
                try {
                  // Attempt to notify the original admin or a default channel if configured
                  await bot.sendMessage(chatId, `⚠️ Error executing automated task "${taskName}": ${error.message}. Check logs.`);
                } catch (notifyError) {
                  secureLog('error', `Failed to send error notification for task ${taskName}: ${notifyError.message}`);
                }
              }
            }, {
              scheduled: true, // Start the job right away
              timezone: process.env.TZ // Use the server's configured timezone
            });

            scheduledTasks[taskName] = {
              cronJob: job,
              schedule: cronSchedule,
              targetGroupId: targetGroupId,
              prompts: prompts,
              running: true,
              createdBy: msg.from.id
            };
            bot.sendMessage(chatId, `✅ Automated task "${taskName}" added and started with schedule "${cronSchedule}". It will send messages to group ID ${targetGroupId}.`);
            secureLog('success', `Automated task "${taskName}" created by user ${msg.from.id}`);
            saveTasksToFile(); // Save tasks after adding
          } catch (error) {
            bot.sendMessage(chatId, `❌ Error creating task "${taskName}": ${error.message}`);
            secureLog('error', `Error creating task ${taskName}: ${error.message}`);
          }
          break;

        case '/listautotasks':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /listautotasks attempt by user ${msg.from.id}`);
            return;
          }
          let taskListMessage = "📋 Configured Automated Tasks:\n\n";
          if (Object.keys(scheduledTasks).length === 0) {
            taskListMessage += "No tasks configured yet. Use /addautotask to add one.";
          } else {
            for (const name in scheduledTasks) {
              const task = scheduledTasks[name];

              taskListMessage += `🔹 Name: ${name}\n`;
              taskListMessage += `   Schedule: ${task.schedule}\n`;
              taskListMessage += `   Target Group ID: ${task.targetGroupId}\n`;
              taskListMessage += `   Status: ${task.running ? '🟢 Running' : '🔴 Stopped'}\n`;
              taskListMessage += `   Prompts: ${task.prompts.join(', ')}\n\n`;
            }
          }
          // Send as plain text without markdown formatting
          taskListMessage = formatAsPlainText(taskListMessage);
          bot.sendMessage(chatId, taskListMessage).catch(error => {
            secureLog('error', `Error sending task list: ${error.message}. Raw message: ${taskListMessage}`);
            // Fallback to basic message
            bot.sendMessage(chatId, "Could not display task list. Please check logs for details.");
          });
          secureLog('success', `Listed automated tasks for user ${msg.from.id}`);
          break;

        case '/toggleautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /toggleautotask attempt by user ${msg.from.id}`);
            return;
          }
          const taskNameToToggle = args.trim();
          if (!scheduledTasks[taskNameToToggle]) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToToggle}" not found.`);
            return;
          }
          const taskToToggle = scheduledTasks[taskNameToToggle];
          if (taskToToggle.running) {
            taskToToggle.cronJob.stop();
            taskToToggle.running = false;
            bot.sendMessage(chatId, `🔴 Automated task "${taskNameToToggle}" stopped.`);
            secureLog('success', `Automated task "${taskNameToToggle}" stopped by user ${msg.from.id}`);
          } else {
            taskToToggle.cronJob.start();
            taskToToggle.running = true;
            bot.sendMessage(chatId, `🟢 Automated task "${taskNameToToggle}" started.`);
            secureLog('success', `Automated task "${taskNameToToggle}" started by user ${msg.from.id}`);
          }
          saveTasksToFile(); // Save tasks after toggling
          break;

        case '/deleteautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /deleteautotask attempt by user ${msg.from.id}`);
            return;
          }
          const taskNameToDelete = args.trim();
          if (!scheduledTasks[taskNameToDelete]) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToDelete}" not found.`);
            return;
          }
          scheduledTasks[taskNameToDelete].cronJob.stop();
          delete scheduledTasks[taskNameToDelete];
          bot.sendMessage(chatId, `🗑️ Automated task "${taskNameToDelete}" deleted.`);
          secureLog('success', `Automated task "${taskNameToDelete}" deleted by user ${msg.from.id}`);
          saveTasksToFile(); // Save tasks after deleting
          break;

        case '/editautotask':
          if (!isUserAdmin(msg.from.id)) {
            bot.sendMessage(chatId, "⛔ You are not authorized to use this command.");
            secureLog('info', `Unauthorized /editautotask attempt by user ${msg.from.id}`);
            return;
          }

          const editArgsRaw = args.match(/\"[^\"]+\"|\S+/g) || [];
          if (editArgsRaw.length < 1) {
            bot.sendMessage(chatId, "Usage: /editautotask <task_name> [--schedule \"new_schedule\"] [--groupid <new_group_id>] [--prompts \"prompt1\" ...]");
            return;
          }

          const taskNameToEdit = editArgsRaw[0];
          const taskToEdit = scheduledTasks[taskNameToEdit];

          if (!taskToEdit) {
            bot.sendMessage(chatId, `❌ Task "${taskNameToEdit}" not found.`);
            return;
          }

          let newScheduleOpt = null;
          let newTargetGroupIdStrOpt = null;
          let newPromptsOpt = null;
          let changesMadeCount = 0;

          let argIndex = 1;
          while(argIndex < editArgsRaw.length) {
            const currentArg = editArgsRaw[argIndex];
            if (currentArg === '--schedule') {
              if (argIndex + 1 < editArgsRaw.length) {
                newScheduleOpt = editArgsRaw[argIndex + 1].replace(/^"|"$/g, ''); // Remove quotes
                argIndex += 2;
                changesMadeCount++;
              } else {
                bot.sendMessage(chatId, "Error: --schedule flag requires a value."); return;
              }
            } else if (currentArg === '--groupid') {
              if (argIndex + 1 < editArgsRaw.length) {
                newTargetGroupIdStrOpt = editArgsRaw[argIndex + 1].replace(/^"|"$/g, ''); // Remove quotes if any
                argIndex += 2;
                changesMadeCount++;
              } else {
                bot.sendMessage(chatId, "Error: --groupid flag requires a value."); return;
              }
            } else if (currentArg === '--prompts') {
              newPromptsOpt = editArgsRaw.slice(argIndex + 1).map(p => p.replace(/^"|"$/g, '')); // Remove quotes
              changesMadeCount++;
              break; // Prompts consume the rest
            } else {
              bot.sendMessage(chatId, `Error: Unknown flag or misplaced argument: ${currentArg}. Usage: /editautotask <task_name> [--schedule ...] [--groupid ...] [--prompts ...]`);
              return;
            }
          }

          if (changesMadeCount === 0) {
            bot.sendMessage(chatId, `No changes specified for task "${taskNameToEdit}". Use flags like --schedule, --groupid, or --prompts.`);
            return;
          }

          // Prepare new values, using existing if not provided
          let finalSchedule = taskToEdit.schedule;
          let finalTargetGroupId = taskToEdit.targetGroupId;
          let finalPrompts = taskToEdit.prompts;
          let updateMessages = [];

          if (newScheduleOpt !== null) {
            if (!cron.validate(newScheduleOpt)) {
              bot.sendMessage(chatId, `❌ Invalid new cron schedule: "${newScheduleOpt}".`);
              return;
            }
            finalSchedule = newScheduleOpt;
            updateMessages.push(`Schedule updated to "${finalSchedule}"`);
          }

          if (newTargetGroupIdStrOpt !== null) {
            const newGroupId = parseInt(newTargetGroupIdStrOpt, 10);
            if (isNaN(newGroupId)) {
              bot.sendMessage(chatId, `❌ Invalid new Target Group ID: "${newTargetGroupIdStrOpt}". Must be a number.`);
              return;
            }
            finalTargetGroupId = newGroupId;
            updateMessages.push(`Target Group ID updated to ${finalTargetGroupId}`);
          }

          if (newPromptsOpt !== null) {
            if (newPromptsOpt.length === 0) {
              bot.sendMessage(chatId, `❌ New prompts list cannot be empty if --prompts flag is used.`);
              return;
            }
            finalPrompts = newPromptsOpt;
            updateMessages.push(`Prompts updated to: "${finalPrompts.join('", "')}"`);
          }

          try {
            const wasRunning = taskToEdit.running;
            if (taskToEdit.cronJob) {
              taskToEdit.cronJob.stop();
            }

            const newJob = cron.schedule(finalSchedule, async () => {
              secureLog('info', `Running edited automated task: ${taskNameToEdit}`);
              const randomPrompt = finalPrompts[Math.floor(Math.random() * finalPrompts.length)];
              try {
                await bot.sendChatAction(finalTargetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${finalTargetGroupId} for ${taskNameToEdit}: ${e.message}`));
                const hustleResponse = await createHustleApiCall([{ role: 'user', content: randomPrompt }], `autotask-${taskNameToEdit}`);
                let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : 'No content from Hustle.';

                messageToSend = formatAsPlainText(messageToSend);
                messageToSend = safeTruncateMessage(messageToSend);
                
                await bot.sendMessage(finalTargetGroupId, messageToSend);
                secureLog('success', `Automated task "${taskNameToEdit}" executed. Prompt: "${randomPrompt}". Response sent to group ${finalTargetGroupId}.`);
              } catch (error) {
                secureLog('error', `Error executing automated task "${taskNameToEdit}": ${error.message}`);
                try {
                  await bot.sendMessage(chatId, `⚠️ Error executing automated task "${taskNameToEdit}": ${error.message}. Check logs.`);
                } catch (notifyError) {
                  secureLog('error', `Failed to send error notification for task ${taskNameToEdit}: ${notifyError.message}`);
                }
              }
            }, {
              scheduled: wasRunning, // Preserve previous running state
              timezone: process.env.TZ
            });

            scheduledTasks[taskNameToEdit] = {
              ...taskToEdit, // Preserve createdBy and other potential fields
              cronJob: newJob,
              schedule: finalSchedule,
              targetGroupId: finalTargetGroupId,
              prompts: finalPrompts,
              running: wasRunning
            };

            bot.sendMessage(chatId, `✅ Task "${taskNameToEdit}" updated successfully.\nChanges: ${updateMessages.join('; ')}`);
            secureLog('success', `Automated task "${taskNameToEdit}" edited by user ${msg.from.id}. Changes: ${updateMessages.join('; ')}`);
            saveTasksToFile(); // Save tasks after editing

          } catch (error) {
            bot.sendMessage(chatId, `❌ Error updating task "${taskNameToEdit}": ${error.message}`);
            secureLog('error', `Error updating task ${taskNameToEdit}: ${error.message}`);
            // Attempt to revert to the old job if possible or just log error
            // For simplicity, we are not attempting a revert here, but in a more complex system, one might.
          }
          break;

        default:
          if (!isMessageProcessed(msg)) {
            try {
              await bot.sendMessage(chatId, 'Unknown command. Try /help to see available commands.');
              secureLog('success', 'Unknown command response sent');
            } catch (error) {
              secureLog('error', 'Error sending unknown command response: ' + error.message);
            }
          }
      }
    }

    // Single message handler for all commands
    bot.on('message', async (msg) => {
      const text = msg.text;
      if (!text || !text.startsWith('/')) return;

      const [command, ...args] = text.split(' ');
      await handleCommand(msg, command.toLowerCase(), args.join(' '));
    });

    // Enhanced error handling for polling
    bot.on('polling_error', (error) => {
      secureLog('error', 'Polling error: ' + error.message);
      // Don't exit on polling errors, let the bot try to recover
    });

    bot.on('error', (error) => {
      secureLog('error', 'Bot error: ' + error.message);
    });

    console.log('\nBot is starting...');
    console.log('Available commands:');
    console.log('- /start');
    console.log('- /help');
    console.log('- /hustle [message]');
    console.log('- /hustlestream [message]');
    console.log('\nAdmin Commands (if ADMIN_USER_IDS is set):');
    console.log('- /addautotask <name> <cron_schedule> <target_group_id> <prompt1> [prompt2...]');
    console.log('- /listautotasks');
    console.log('- /toggleautotask <name>');
    console.log('- /deleteautotask <name>');
    console.log('- /editautotask <name> [--schedule "new_schedule"] [--groupid <new_group_id>] [--prompts "prompt1" ...]');
    console.log('\n⏳ Connecting to Telegram...');
}); // End of loadTasksFromFile().then()

// Function to save tasks to a file
async function saveTasksToFile() {
  secureLog('info', 'Attempting to save tasks to file...');
  try {
    const tasksToSave = {};
    for (const taskName in scheduledTasks) {
      const task = scheduledTasks[taskName];
      tasksToSave[taskName] = {
        schedule: task.schedule,
        targetGroupId: task.targetGroupId,
        prompts: task.prompts,
        running: task.running,
        createdBy: task.createdBy
      };
    }
    const dataToWrite = JSON.stringify(tasksToSave, null, 2);
    secureLog('info', `Data to write to ${TASKS_FILE_PATH}:\n${dataToWrite}`);
    await fs.writeFile(TASKS_FILE_PATH, dataToWrite);
    secureLog('success', 'Scheduled tasks saved successfully to file.');
  } catch (error) {
    secureLog('error', `Failed to save tasks to file: ${error.message}`);
  }
}

// Function to load tasks from a file and re-initialize them
async function loadTasksFromFile() {
  secureLog('info', `Attempting to load tasks from ${TASKS_FILE_PATH}...`);
  try {
    const data = await fs.readFile(TASKS_FILE_PATH, 'utf8');
    secureLog('info', `Data read from ${TASKS_FILE_PATH}:\n${data}`);
    const loadedTasksConfig = JSON.parse(data);
    let reinitializedCount = 0;

    // Explicitly capture CONFIG and hustleClient in this scope for use by the cron jobs
    const CONFIG_FOR_TASKS = CONFIG;
    // const HUSTLE_CLIENT_FOR_TASKS = hustleClient; // Capture hustleClient // <<< This should now work as hustleClient is in scope

    for (const taskName in loadedTasksConfig) {
      const config = loadedTasksConfig[taskName];
      try {
        if (!cron.validate(config.schedule)) {
          secureLog('error', `Skipping task "${taskName}" from file: Invalid cron schedule "${config.schedule}".`);
          continue;
        }

        const job = cron.schedule(config.schedule, async () => {
          secureLog('info', `Running automated task (from file): ${taskName}`);
          const randomPrompt = config.prompts[Math.floor(Math.random() * config.prompts.length)];
          try {
            await bot.sendChatAction(config.targetGroupId, 'typing').catch(e => secureLog('error', `Error sending chat action to ${config.targetGroupId} for ${taskName}: ${e.message}`));
            const hustleResponse = await createHustleApiCall([{ role: 'user', content: randomPrompt }], `autotask-${taskName}`);
            let messageToSend = typeof hustleResponse === 'string' ? hustleResponse : 'No content from Hustle.';

            messageToSend = formatAsPlainText(messageToSend);
            messageToSend = safeTruncateMessage(messageToSend);
            
            await bot.sendMessage(config.targetGroupId, messageToSend);
            secureLog('success', `Automated task "${taskName}" (from file) executed. Prompt: "${randomPrompt}". Response sent to group ${config.targetGroupId}.`);
          } catch (execError) {
            secureLog('error', `Error executing automated task "${taskName}" (from file): ${execError.message}`);
          }
        }, {
          scheduled: config.running,
          timezone: process.env.TZ
        });

        scheduledTasks[taskName] = {
          cronJob: job,
          schedule: config.schedule,
          targetGroupId: config.targetGroupId,
          prompts: config.prompts,
          running: config.running,
          createdBy: config.createdBy
        };
        if (config.running) {
           // job.start(); // Already handled by scheduled: true in cron.schedule if config.running is true
        } else {
           // job.stop(); // Already handled by scheduled: false in cron.schedule if config.running is false
        }
        reinitializedCount++;
      } catch (taskInitError) {
        secureLog('error', `Error re-initializing task "${taskName}" from file: ${taskInitError.message}`);
      }
    }
    if (reinitializedCount > 0) {
        secureLog('info', `Successfully loaded and re-initialized ${reinitializedCount} tasks from file.`);
    }
  } catch (error) {
    if (error.code === 'ENOENT') {
      secureLog('info', `No tasks file found (${TASKS_FILE_PATH}). Starting with no scheduled tasks.`);
    } else {
      secureLog('error', `Failed to load tasks from file: ${error.message}`);
      secureLog('error', `Error details during load: ${JSON.stringify(error)}`);
    }
  }
} 